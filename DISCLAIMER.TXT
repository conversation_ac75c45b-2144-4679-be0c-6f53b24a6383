若您需要获得本服务，您（以下称"用户"）应当同意本协议的全部条款并按照页面上的提示完成全部申请使用程序。您可以在源码或者发布程序中找到 DISCLAIMER.TXT，或者查看以下副本。

为了下载和使用由 firerpa（地址：github.com/firerpa，邮箱：<EMAIL>，以下简称“开发者”）开发的 LAMDA 软件（以下简称“本服务”），您必须仔细阅读并同意本协议中的所有条款。请确保您在下载、安装或使用本软件前，已充分理解并同意以下内容。
在未完全阅读并接受本协议条款之前，您无权下载、安装或使用本软件及其相关服务。一旦您进行下载、安装或使用本软件的操作，即视为您已阅读并同意本协议的全部条款，并愿意遵守其约束。

风险告知：
本服务需要设备获取 root 权限才能运行，且默认的通讯协议及相关证书文件均为开放信息，这可能会增加您设备被入侵的风险。
本服务可能存在未知的逻辑错误，可能会导致潜在的风险如数据丢失、系统崩溃等，由用户自行决定是否下载、使用本服务。


1、本服务设计目的为提高安全分析及测试人员工作效率，应用行为、应用合规分析等。提供的相关工具均为合法合规的APP测试分析、Mock 场景提供。
本服务本身不提供任何侵入、修改、抓取其他应用内存及网络数据的功能，整合了各大开源框架提供的服务供用户自行选择，方便安全分析人员使用，减少用户的重复性劳动以及管理成本。
本服务本身不存在盈利性，用户可根据自己需求自行通过下载获取使用，下载及使用过程中不会收取任何费用。

2、本服务尊重并保护用户的个人隐私，不会窃取任何用户设备中的信息。本服务的启动及任何对设备数据读取、存储、传输等权利均在用户自己手中。

3、用户必须在无隐私数据的虚拟设备或者专用设备中使用本服务。在使用本服务时，必须遵守中华人民共和国或者用户所属国家或地区的法律法规，
不得为任何非法目的而使用本服务，不得利用本服务进行任何不利于他人的行为。

4、用户只可使用本服务进行正规的学习研究或是经过合法授权的应用分析、测试等行为，若用户在使用该软件服务的过程中违背以上原则对第三方造成损失，一切责任由该用户自行承担。

5、任何单位或个人因下载使用本服务而产生的任何意外、疏忽、合约毁坏、诽谤、版权或知识产权侵犯及其造成的损失 (包括但不限于直接、间接、附带或衍生的损失等)，开发者不承担任何法律责任。

6、您可以将本服务用于商业用途，但仅限于通过本服务提供的功能、接口或相关服务进行衍生功能扩展或产品开发。您同意，不得将本服务及其相关服务或接口用于任何违反当地法律法规，或从事损害他人利益的行为。

7、用户明确并同意本协议条款列举的全部内容，对使用本服务过程中可能存在的风险和后果由用户自行承担，开发者不承担任何法律责任。

8、开发者有权随时对本声明条款及附件内容进行单方面的变更、中断或终止部分或全部本服务的权利。并以消息推送、网页公告等方式予以公布，
公布后立即自动生效，无需另行单独通知；若您在本声明内容公告变更后继续使用的，表示您已充分阅读、理解并接受修改后的声明内容。

9、如果本声明的任何部分被认为无效或不可执行，则该部分应以符合相关法律的方式予以修正，以尽可能地反映出开发者的原始意图，
其余部分仍具有完全效力。不可执行的部分声明，并不构成开发者放弃执行该声明的权利。

10、保留权利：未明示授权的其他一切权利均由开发者所有。


请确认您已阅读并接受本协议所有条款，否则您将无权下载、安装或使用本软件及相关服务。