# openvpn Docker image config
# https://github.com/rev1si0n/lamda/tree/master/tools/openvpn
#
topology subnet
server *********** *************

proto udp
port 1190

dev tun

# 不要编辑下面几行 easy-rsa/pki 配置
ca         /etc/openvpn/easy-rsa/pki/ca.crt
dh         /etc/openvpn/easy-rsa/pki/dh.pem
cert       /etc/openvpn/easy-rsa/pki/issued/lamda.crt
key        /etc/openvpn/easy-rsa/pki/private/lamda.key
crl-verify /etc/openvpn/easy-rsa/pki/crl.pem

# TLS crypt 配置二选一
;tls-auth   /etc/openvpn/ta.key 0
tls-crypt  /etc/openvpn/ta.key

cipher AES-256-GCM
keepalive 15 120

persist-tun
persist-key

ifconfig-pool-persist ipp.txt 10
client-to-client

client-config-dir ccd
script-security 2

compress lz4-v2
push "compress lz4-v2"

max-clients 254

# 如果你想路由其他服务器上的网段到客户端
;push "route *********** *************"

# 如果你想要让每个客户端使用特定 DNS
;push "dhcp-option DNS ***************"

verb 4