// Copyright 2022 rev1si0n (<EMAIL>). All rights reserved.
//
// Distributed under MIT license.
// See file LICENSE for detail or copy at https://opensource.org/licenses/MIT
syntax = "proto3";
package lamda.rpc;

message Point {
        uint32 x            = 1;
        uint32 y            = 2;
}

message Bound {
        uint32 top          = 1;
        uint32 left         = 2;
        uint32 bottom       = 3;
        uint32 right        = 4;
}

enum Orientation {
        ORIEN_NATURE        = 0;
        ORIEN_LEFT          = 1;
        ORIEN_UPSIDEDOWN    = 2;
        ORIEN_RIGHT         = 3;
}

enum Key {
        KEY_BACK            = 0;
        KEY_CAMERA          = 1;
        KEY_CENTER          = 2;
        KEY_DELETE          = 3;
        KEY_DOWN            = 4;
        KEY_ENTER           = 5;
        KEY_HOME            = 6;
        KEY_LEFT            = 7;
        KEY_MENU            = 8;
        KEY_POWER           = 9;
        KEY_RECENT          = 10;
        KEY_RIGHT           = 11;
        KEY_SEARCH          = 12;
        KEY_UP              = 13;
        KEY_VOLUME_DOWN     = 14;
        KEY_VOLUME_MUTE     = 15;
        KEY_VOLUME_UP       = 16;
}

enum KeyCode {
        KEYCODE_UNKNOWN         = 0;
        KEYCODE_SOFT_LEFT       = 1;
        KEYCODE_SOFT_RIGHT      = 2;
        KEYCODE_HOME            = 3;
        KEYCODE_BACK            = 4;
        KEYCODE_CALL            = 5;
        KEYCODE_ENDCALL         = 6;
        KEYCODE_0               = 7;
        KEYCODE_1               = 8;
        KEYCODE_2               = 9;
        KEYCODE_3               = 10;
        KEYCODE_4               = 11;
        KEYCODE_5               = 12;
        KEYCODE_6               = 13;
        KEYCODE_7               = 14;
        KEYCODE_8               = 15;
        KEYCODE_9               = 16;
        KEYCODE_STAR            = 17;
        KEYCODE_POUND           = 18;
        KEYCODE_DPAD_UP         = 19;
        KEYCODE_DPAD_DOWN       = 20;
        KEYCODE_DPAD_LEFT       = 21;
        KEYCODE_DPAD_RIGHT      = 22;
        KEYCODE_DPAD_CENTER     = 23;
        KEYCODE_VOLUME_UP       = 24;
        KEYCODE_VOLUME_DOWN     = 25;
        KEYCODE_POWER           = 26;
        KEYCODE_CAMERA          = 27;
        KEYCODE_CLEAR           = 28;
        KEYCODE_A               = 29;
        KEYCODE_B               = 30;
        KEYCODE_C               = 31;
        KEYCODE_D               = 32;
        KEYCODE_E               = 33;
        KEYCODE_F               = 34;
        KEYCODE_G               = 35;
        KEYCODE_H               = 36;
        KEYCODE_I               = 37;
        KEYCODE_J               = 38;
        KEYCODE_K               = 39;
        KEYCODE_L               = 40;
        KEYCODE_M               = 41;
        KEYCODE_N               = 42;
        KEYCODE_O               = 43;
        KEYCODE_P               = 44;
        KEYCODE_Q               = 45;
        KEYCODE_R               = 46;
        KEYCODE_S               = 47;
        KEYCODE_T               = 48;
        KEYCODE_U               = 49;
        KEYCODE_V               = 50;
        KEYCODE_W               = 51;
        KEYCODE_X               = 52;
        KEYCODE_Y               = 53;
        KEYCODE_Z               = 54;
        KEYCODE_COMMA           = 55;
        KEYCODE_PERIOD          = 56;
        KEYCODE_ALT_LEFT        = 57;
        KEYCODE_ALT_RIGHT       = 58;
        KEYCODE_SHIFT_LEFT      = 59;
        KEYCODE_SHIFT_RIGHT     = 60;
        KEYCODE_TAB             = 61;
        KEYCODE_SPACE           = 62;
        KEYCODE_SYM             = 63;
        KEYCODE_EXPLORER        = 64;
        KEYCODE_ENVELOPE        = 65;
        KEYCODE_ENTER           = 66;
        KEYCODE_DEL             = 67;
        KEYCODE_GRAVE           = 68;
        KEYCODE_MINUS           = 69;
        KEYCODE_EQUALS          = 70;
        KEYCODE_LEFT_BRACKET    = 71;
        KEYCODE_RIGHT_BRACKET   = 72;
        KEYCODE_BACKSLASH       = 73;
        KEYCODE_SEMICOLON       = 74;
        KEYCODE_APOSTROPHE      = 75;
        KEYCODE_SLASH           = 76;
        KEYCODE_AT              = 77;
        KEYCODE_NUM             = 78;
        KEYCODE_HEADSETHOOK     = 79;
        KEYCODE_FOCUS           = 80;
        KEYCODE_PLUS            = 81;
        KEYCODE_MENU            = 82;
        KEYCODE_NOTIFICATION    = 83;
        KEYCODE_SEARCH          = 84;
        KEYCODE_MEDIA_PLAY_PAUSE= 85;
        KEYCODE_MEDIA_STOP      = 86;
        KEYCODE_MEDIA_NEXT      = 87;
        KEYCODE_MEDIA_PREVIOUS  = 88;
        KEYCODE_MEDIA_REWIND    = 89;
        KEYCODE_MEDIA_FAST_FORWARD = 90;
        KEYCODE_MUTE            = 91;
        KEYCODE_PAGE_UP         = 92;
        KEYCODE_PAGE_DOWN       = 93;
        KEYCODE_PICTSYMBOLS     = 94;
        KEYCODE_SWITCH_CHARSET  = 95;
        KEYCODE_BUTTON_A        = 96;
        KEYCODE_BUTTON_B        = 97;
        KEYCODE_BUTTON_C        = 98;
        KEYCODE_BUTTON_X        = 99;
        KEYCODE_BUTTON_Y        = 100;
        KEYCODE_BUTTON_Z        = 101;
        KEYCODE_BUTTON_L1       = 102;
        KEYCODE_BUTTON_R1       = 103;
        KEYCODE_BUTTON_L2       = 104;
        KEYCODE_BUTTON_R2       = 105;
        KEYCODE_BUTTON_THUMBL   = 106;
        KEYCODE_BUTTON_THUMBR   = 107;
        KEYCODE_BUTTON_START    = 108;
        KEYCODE_BUTTON_SELECT   = 109;
        KEYCODE_BUTTON_MODE     = 110;
        KEYCODE_ESCAPE          = 111;
        KEYCODE_FORWARD_DEL     = 112;
        KEYCODE_CTRL_LEFT       = 113;
        KEYCODE_CTRL_RIGHT      = 114;
        KEYCODE_CAPS_LOCK       = 115;
        KEYCODE_SCROLL_LOCK     = 116;
        KEYCODE_META_LEFT       = 117;
        KEYCODE_META_RIGHT      = 118;
        KEYCODE_FUNCTION        = 119;
        KEYCODE_SYSRQ           = 120;
        KEYCODE_BREAK           = 121;
        KEYCODE_MOVE_HOME       = 122;
        KEYCODE_MOVE_END        = 123;
        KEYCODE_INSERT          = 124;
        KEYCODE_FORWARD         = 125;
        KEYCODE_MEDIA_PLAY      = 126;
        KEYCODE_MEDIA_PAUSE     = 127;
        KEYCODE_MEDIA_CLOSE     = 128;
        KEYCODE_MEDIA_EJECT     = 129;
        KEYCODE_MEDIA_RECORD    = 130;
        KEYCODE_F1              = 131;
        KEYCODE_F2              = 132;
        KEYCODE_F3              = 133;
        KEYCODE_F4              = 134;
        KEYCODE_F5              = 135;
        KEYCODE_F6              = 136;
        KEYCODE_F7              = 137;
        KEYCODE_F8              = 138;
        KEYCODE_F9              = 139;
        KEYCODE_F10             = 140;
        KEYCODE_F11             = 141;
        KEYCODE_F12             = 142;
        KEYCODE_NUM_LOCK        = 143;
        KEYCODE_NUMPAD_0        = 144;
        KEYCODE_NUMPAD_1        = 145;
        KEYCODE_NUMPAD_2        = 146;
        KEYCODE_NUMPAD_3        = 147;
        KEYCODE_NUMPAD_4        = 148;
        KEYCODE_NUMPAD_5        = 149;
        KEYCODE_NUMPAD_6        = 150;
        KEYCODE_NUMPAD_7        = 151;
        KEYCODE_NUMPAD_8        = 152;
        KEYCODE_NUMPAD_9        = 153;
        KEYCODE_NUMPAD_DIVIDE   = 154;
        KEYCODE_NUMPAD_MULTIPLY = 155;
        KEYCODE_NUMPAD_SUBTRACT = 156;
        KEYCODE_NUMPAD_ADD      = 157;
        KEYCODE_NUMPAD_DOT      = 158;
        KEYCODE_NUMPAD_COMMA    = 159;
        KEYCODE_NUMPAD_ENTER    = 160;
        KEYCODE_NUMPAD_EQUALS   = 161;
        KEYCODE_NUMPAD_LEFT_PAREN = 162;
        KEYCODE_NUMPAD_RIGHT_PAREN = 163;
        KEYCODE_VOLUME_MUTE     = 164;
        KEYCODE_INFO            = 165;
        KEYCODE_CHANNEL_UP      = 166;
        KEYCODE_CHANNEL_DOWN    = 167;
        KEYCODE_ZOOM_IN         = 168;
        KEYCODE_ZOOM_OUT        = 169;
        KEYCODE_TV              = 170;
        KEYCODE_WINDOW          = 171;
        KEYCODE_GUIDE           = 172;
        KEYCODE_DVR             = 173;
        KEYCODE_BOOKMARK        = 174;
        KEYCODE_CAPTIONS        = 175;
        KEYCODE_SETTINGS        = 176;
        KEYCODE_TV_POWER        = 177;
        KEYCODE_TV_INPUT        = 178;
        KEYCODE_STB_POWER       = 179;
        KEYCODE_STB_INPUT       = 180;
        KEYCODE_AVR_POWER       = 181;
        KEYCODE_AVR_INPUT       = 182;
        KEYCODE_PROG_RED        = 183;
        KEYCODE_PROG_GREEN      = 184;
        KEYCODE_PROG_YELLOW     = 185;
        KEYCODE_PROG_BLUE       = 186;
        KEYCODE_APP_SWITCH      = 187;
        KEYCODE_BUTTON_1        = 188;
        KEYCODE_BUTTON_2        = 189;
        KEYCODE_BUTTON_3        = 190;
        KEYCODE_BUTTON_4        = 191;
        KEYCODE_BUTTON_5        = 192;
        KEYCODE_BUTTON_6        = 193;
        KEYCODE_BUTTON_7        = 194;
        KEYCODE_BUTTON_8        = 195;
        KEYCODE_BUTTON_9        = 196;
        KEYCODE_BUTTON_10       = 197;
        KEYCODE_BUTTON_11       = 198;
        KEYCODE_BUTTON_12       = 199;
        KEYCODE_BUTTON_13       = 200;
        KEYCODE_BUTTON_14       = 201;
        KEYCODE_BUTTON_15       = 202;
        KEYCODE_BUTTON_16       = 203;
        KEYCODE_LANGUAGE_SWITCH = 204;
        KEYCODE_MANNER_MODE     = 205;
        KEYCODE_3D_MODE         = 206;
        KEYCODE_CONTACTS        = 207;
        KEYCODE_CALENDAR        = 208;
        KEYCODE_MUSIC           = 209;
        KEYCODE_CALCULATOR      = 210;
        KEYCODE_ZENKAKU_HANKAKU = 211;
        KEYCODE_EISU            = 212;
        KEYCODE_MUHENKAN        = 213;
        KEYCODE_HENKAN          = 214;
        KEYCODE_KATAKANA_HIRAGANA = 215;
        KEYCODE_YEN             = 216;
        KEYCODE_RO              = 217;
        KEYCODE_KANA            = 218;
        KEYCODE_ASSIST          = 219;
        KEYCODE_BRIGHTNESS_DOWN = 220;
        KEYCODE_BRIGHTNESS_UP   = 221;
        KEYCODE_MEDIA_AUDIO_TRACK = 222;
        KEYCODE_SLEEP           = 223;
        KEYCODE_WAKEUP          = 224;
        KEYCODE_PAIRING         = 225;
        KEYCODE_MEDIA_TOP_MENU  = 226;
        KEYCODE_11              = 227;
        KEYCODE_12              = 228;
        KEYCODE_LAST_CHANNEL    = 229;
        KEYCODE_TV_DATA_SERVICE = 230;
        KEYCODE_VOICE_ASSIST    = 231;
        KEYCODE_TV_RADIO_SERVICE = 232;
        KEYCODE_TV_TELETEXT     = 233;
        KEYCODE_TV_NUMBER_ENTRY = 234;
        KEYCODE_TV_TERRESTRIAL_ANALOG = 235;
        KEYCODE_TV_TERRESTRIAL_DIGITAL = 236;
        KEYCODE_TV_SATELLITE    = 237;
        KEYCODE_TV_SATELLITE_BS = 238;
        KEYCODE_TV_SATELLITE_CS = 239;
        KEYCODE_TV_SATELLITE_SERVICE = 240;
        KEYCODE_TV_NETWORK      = 241;
        KEYCODE_TV_ANTENNA_CABLE = 242;
        KEYCODE_TV_INPUT_HDMI_1 = 243;
        KEYCODE_TV_INPUT_HDMI_2 = 244;
        KEYCODE_TV_INPUT_HDMI_3 = 245;
        KEYCODE_TV_INPUT_HDMI_4 = 246;
        KEYCODE_TV_INPUT_COMPOSITE_1 = 247;
        KEYCODE_TV_INPUT_COMPOSITE_2 = 248;
        KEYCODE_TV_INPUT_COMPONENT_1 = 249;
        KEYCODE_TV_INPUT_COMPONENT_2 = 250;
        KEYCODE_TV_INPUT_VGA_1  = 251;
        KEYCODE_TV_AUDIO_DESCRIPTION = 252;
        KEYCODE_TV_AUDIO_DESCRIPTION_MIX_UP = 253;
        KEYCODE_TV_AUDIO_DESCRIPTION_MIX_DOWN = 254;
        KEYCODE_TV_ZOOM_MODE    = 255;
        KEYCODE_TV_CONTENTS_MENU = 256;
        KEYCODE_TV_MEDIA_CONTEXT_MENU = 257;
        KEYCODE_TV_TIMER_PROGRAMMING = 258;
        KEYCODE_HELP            = 259;
        KEYCODE_NAVIGATE_PREVIOUS = 260;
        KEYCODE_NAVIGATE_NEXT   = 261;
        KEYCODE_NAVIGATE_IN     = 262;
        KEYCODE_NAVIGATE_OUT    = 263;
        KEYCODE_STEM_PRIMARY    = 264;
        KEYCODE_STEM_1          = 265;
        KEYCODE_STEM_2          = 266;
        KEYCODE_STEM_3          = 267;
        KEYCODE_DPAD_UP_LEFT    = 268;
        KEYCODE_DPAD_DOWN_LEFT  = 269;
        KEYCODE_DPAD_UP_RIGHT   = 270;
        KEYCODE_DPAD_DOWN_RIGHT = 271;
        KEYCODE_MEDIA_SKIP_FORWARD = 272;
        KEYCODE_MEDIA_SKIP_BACKWARD = 273;
        KEYCODE_MEDIA_STEP_FORWARD = 274;
        KEYCODE_MEDIA_STEP_BACKWARD = 275;
        KEYCODE_SOFT_SLEEP      = 276;
        KEYCODE_CUT             = 277;
        KEYCODE_COPY            = 278;
        KEYCODE_PASTE           = 279;
        KEYCODE_SYSTEM_NAVIGATION_UP = 280;
        KEYCODE_SYSTEM_NAVIGATION_DOWN = 281;
        KEYCODE_SYSTEM_NAVIGATION_LEFT = 282;
        KEYCODE_SYSTEM_NAVIGATION_RIGHT = 283;
        KEYCODE_ALL_APPS        = 284;
        KEYCODE_REFRESH         = 285;
        KEYCODE_THUMBS_UP       = 286;
        KEYCODE_THUMBS_DOWN     = 287;
        KEYCODE_PROFILE_SWITCH  = 288;
}

enum MetaKeyCode {
        META_UNKNOWN            = 0;
        META_SHIFT_ON           = 1;
        META_ALT_ON             = 2;
        META_SYM_ON             = 4;
        META_FUNCTION_ON        = 8;
        META_ALT_LEFT_ON        = 16;
        META_ALT_RIGHT_ON       = 32;
        META_ALT_MASK           = 50;
        META_SHIFT_LEFT_ON      = 64;
        META_SHIFT_RIGHT_ON     = 128;
        META_SHIFT_MASK         = 193;
        META_CTRL_ON            = 4096;
        META_CTRL_LEFT_ON       = 8192;
        META_CTRL_RIGHT_ON      = 16384;
        META_CTRL_MASK          = 28672;
        META_META_ON            = 65536;
        META_META_LEFT_ON       = 131072;
        META_META_RIGHT_ON      = 262144;
        META_META_MASK          = 458752;
        META_CAPS_LOCK_ON       = 1048576;
        META_NUM_LOCK_ON        = 2097152;
        META_SCROLL_LOCK_ON     = 4194304;
}

enum Corner {
        COR_CENTER          = 0;
        COR_BOTTOMRIGHT     = 1;
        COR_TOPLEFT         = 2;
}

enum Direction {
        DIR_UP              = 0;
        DIR_LEFT            = 1;
        DIR_DOWN            = 2;
        DIR_RIGHT           = 3;
}

message ObjInfo {
        Bound bounds        = 1;
        bool checkable      = 2;
        bool checked        = 3;
        uint32 childCount   = 4;
        string className    = 5;
        bool clickable      = 6;
        string contentDescription = 7;
        bool enabled        = 8;
        bool focusable      = 9;
        bool focused        = 10;
        bool longClickable  = 11;
        string packageName  = 12;
        string resourceName = 13;
        bool scrollable     = 14;
        bool selected       = 15;
        string text         = 16;
        Bound visibleBounds = 17;
}

message ToastInfo {
        uint64 timestamp    = 1;
        string package      = 2;
        string message      = 3;
}

message ObjInfoList {
        repeated ObjInfo objects = 1;
}

message Selector {
        uint32 mask                     = 1;
        string text                     = 2;
        string textContains             = 3;
        string textMatches              = 4;
        string textStartsWith           = 5;
        string className                = 6;
        string classNameMatches         = 7;
        string description              = 8;
        string descriptionContains      = 9;
        string descriptionMatches       = 10;
        string descriptionStartsWith    = 11;
        bool   checkable                = 12;
        bool   checked                  = 13;
        bool   clickable                = 14;
        bool   longClickable            = 15;
        bool   scrollable               = 16;
        bool   enabled                  = 17;
        bool   focusable                = 18;
        bool   focused                  = 19;
        bool   selected                 = 20;
        string packageName              = 21;
        string packageNameMatches       = 22;
        string resourceId               = 23;
        string resourceIdMatches        = 24;
        uint32 index                    = 25;
        uint32 instance                 = 26;
        repeated string childOrSibling  = 27;
        repeated Selector childOrSiblingSelector  = 28;
        repeated string fields          = 50;
}

message DeviceInfo {
        string productName              = 1;
        uint32 sdkInt                   = 2;
        uint32 displayHeight            = 3;
        uint32 displayRotation          = 4;
        uint32 displaySizeDpX           = 5;
        uint32 displaySizeDpY           = 6;
        uint32 displayWidth             = 7;
        bool screenLocked               = 8;
        bool screenOn                   = 9;
        bool naturalOrientation         = 10;
        string currentPackageName       = 11;
}

message SelectorTakeScreenshotRequest {
        Selector selector         = 1;
        uint32 quality            = 3;
}
message SelectorSetTextRequest {
        Selector selector         = 1;
        string text               = 3;
}
message SelectorClickRequest {
        Selector selector         = 1;
        Corner corner             = 3;
}
message SelectorOnlyRequest {
        Selector selector         = 1;
}
message SelectorDragToRequest {
        Selector selector         = 1;
        oneof _target {
                Selector target   = 3;
                Point point       = 4;
        }
        uint32 step               = 5;
}
message SelectorWaitRequest {
        Selector selector         = 1;
        uint32 timeout            = 3;
}
message SelectorSwipeRequest {
        Selector selector         = 1;
        Direction direction       = 3;
        uint32 step               = 4;
}
message SelectorFlingRequest {
        Selector selector         = 1;
        bool vertical             = 3;
        uint32 maxSwipes           = 4;
}
message SelectorScrollRequest {
        Selector selector         = 1;
        Selector target           = 2;
        bool vertical             = 3;
        uint32 maxSwipes          = 4;
        uint32 step               = 5;
}

message SelectorPinchRequest {
        Selector selector         = 1;
        uint32 percent            = 3;
        uint32 step               = 4;
}

message ClickPointRequest {
        Point point     = 1;
}

message DragPointRequest {
        Point A         = 1;
        Point B         = 2;
        uint32 step     = 3;
}

message SwipePointRequest {
        Point A         = 1;
        Point B         = 2;
        uint32 step     = 3;
}

message SwipePointsRequest {
        repeated Point points = 1;
        uint32 step     = 2;
}

message OrientationRequest {
        Orientation orientation = 1;
}

message PressKeyRequest {
        Key key         = 1;
        uint32 code     = 2;
        uint32 meta     = 3;
}

message TakeScreenshotRequest {
        Bound bound     = 1;
        uint32 quality  = 2;
}

message ClipboardRequest {
        string ID       = 1;
        string value    = 2;
}

message WatcherNameList {
        repeated string watchers = 1;
}

message WatcherRegistRequest {
        string name     = 1;
        repeated Selector selectors = 2;
        oneof _target {
                Selector target = 3;
                Key key = 4;
        }
}

enum FindImageMethod {
        FIM_TEMPLATE            = 0;
        FIM_FEATURE             = 1;
}

enum FindImageArea {
        FIA_WHOLE_SCREEN        = 0;
        FIA_LEFT                = 1;
        FIA_TOP_LEFT            = 2;
        FIA_TOP                 = 3;
        FIA_TOP_RIGHT           = 4;
        FIA_RIGHT               = 5;
        FIA_BOTTOM_RIGHT        = 6;
        FIA_BOTTOM              = 7;
        FIA_BOTTOM_LEFT         = 8;
}

message FindImageRequest {
        bytes partial           = 1;
        FindImageMethod method  = 2;
        oneof _area {
                FindImageArea area = 3;
                Bound bound     = 4;
        }
        uint32 distance         = 5;
        float threshold         = 6;
        float scale             = 7;
}

message FindImageResponse {
        repeated Bound bounds = 1;
}