// Copyright 2022 rev1si0n (<EMAIL>). All rights reserved.
//
// Distributed under MIT license.
// See file LICENSE for detail or copy at https://opensource.org/licenses/MIT
syntax = "proto3";
package lamda.rpc;

message ShellRequest {
        string tid        = 1;
        string name       = 2;
        string script     = 3;
        int32  timeout    = 4;
}

message ShellResult {
        int32  exitstatus = 1;
        bytes  stdout     = 2;
        bytes  stderr     = 3;
}

message ShellTask {
        string tid        = 1;
        string name       = 2;
        int32  pid        = 3;
}