// Copyright 2022 rev1si0n (<EMAIL>). All rights reserved.
//
// Distributed under MIT license.
// See file LICENSE for detail or copy at https://opensource.org/licenses/MIT
syntax = "proto3";
package lamda.rpc;

message Boolean {
        bool   value  = 1;
}

message Integer {
        int64  value  = 1;
}

message String {
        string value  = 1;
}

message Bytes {
        bytes  value  = 1;
}

message Empty {
}
