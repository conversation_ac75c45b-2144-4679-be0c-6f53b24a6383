#!/bin/bash
mkdir -p /etc/openvpn/ccd
mkdir -p /etc/openvpn/easy-rsa

if [ ! -f ${OVPNCONFIG} ]; then
cp /root/config.ovpn ${OVPNCONFIG}
fi

if [ ! -f ${VARS} ]; then
cp /root/vars ${VARS}
fi

set -ex
openvpn --genkey --secret /etc/openvpn/ta.key
openvpn --genkey tls-crypt-v2-server /etc/openvpn/tls-crypt-v2-server.key
mkdir -p /etc/openvpn/tls-crypt-v2-client

easyrsa init-pki
easyrsa build-ca nopass
easyrsa gen-dh
easyrsa build-server-full lamda nopass
easyrsa gen-crl
