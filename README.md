<p align="center">
<img src="image/logo.svg" alt="FIRERPA" width="345">
</p>

<p align="center">Android AI Robot Framework, Next-Generation Mobile Data Automation Robot</p>

<p align="center">
<img src="https://img.shields.io/badge/python-3.6+-blue.svg?logo=python&labelColor=yellow" />
<img src="https://img.shields.io/badge/android-6.0--15-blue.svg?logo=android&labelColor=white" />
<img src="https://img.shields.io/badge/root%20require-red.svg?logo=android&labelColor=black" />
<img src="https://img.shields.io/github/downloads/rev1si0n/lamda/total" />
<img src="https://img.shields.io/github/v/release/rev1si0n/lamda" />
<br>
<img src="https://img.shields.io/badge/Model%20Context%20Protocol%20(MCP)%20SUPPORTED-000.svg?logo=openai&labelColor=black" />
</p>

<p align="center"><a href="https://device-farm.com/doc/en/">Document (English)</a> | <a href="https://device-farm.com/doc/">中文文档</a> | <a href="https://t.me/lamda_dev">TELEGRAM</a> | <a href="https://qm.qq.com/q/zDaX2a594I">QQ Group</a></p>

<h3><p align="center">Most powerful Android automation framework built for the mobile-first era.</p></h3>
<p align="center">面向移动优先时代的强大 Android 自动化框架。</p>

As traditional web platforms decline and smart devices rise, automation and data technologies must quickly adapt to a mobile-first world. FIRERPA meets this demand by offering a modern solution for Android automation. It is lightweight, has no external dependencies, and runs on any version of Android. It features low-latency remote desktop and audio streaming (<80ms), over 160 programmable control interfaces, exceptional stability, distributed deployment support, and easy monitoring and management. 随着传统网页的式微与智能设备的崛起，自动化与数据技术迫切需要适应移动化转型。FIRERPA 顺应这一趋势，为 Android 自动化带来现代化解决方案。轻量，无任何外部依赖，可运行于任何版本的安卓系统。低延迟的远程桌面及远程音频传输（< 80ms）。160+ 编程控制接口，极致稳定，支持分布式部署，易监控管理。

<p align="center">
<img src="image/claude.gif" alt="Claude" width="95%">
</p>

<h3><p align="center">Reliable across Android 6.0 to 15, from emulators to cloud.</p></h3>
<p align="center">兼容 Android 6.0–15，适用于模拟器、真机与云平台。</p>

FIRERPA delivers commercial-grade automation capabilities across a wide range of Android versions and devices. It requires only root access, simplifies security analysis and testing, and is already widely used in scenarios such as digital forensics and compliance monitoring. FIRERPA 提供稳定且达到商用级的自动化能力，兼容众多 Android 系统与设备，仅需 Root 权限即可轻松完成安全分析与测试，广泛应用于数字取证与合规监测等场景。

<h3><p align="center">Zero-Intrusion Design.</p></h3>
<p align="center">零侵入式设计。</p>

FIRERPA ensures complete non-intrusiveness during operation—it does not alter system settings or write to system files, preserving the original environment and ensuring device integrity.
FIRERPA 保证运行过程完全无侵入，不修改系统设置、不改写系统文件，保留原始环境，设备完整性不受影响。

<h3><p align="center">Designed from the ground up for flexible deployment.</p></h3>
<p align="center">从零设计，专为灵活性与大规模部署而生。</p>

FIRERPA was built from the ground up to run non-intrusively on most Android devices. It requires no third-party dependencies, no complex configuration, and is ready to use out of the box. Compared to other solutions, it avoids common issues like instability and poor compatibility, making it ideal for large-scale business applications. FIRERPA 从设计之初就面向多样化环境，几乎兼容所有 Android 设备，无侵入式运行，无需依赖与额外配置，即开即用。相较于其他方案常见的不稳定、兼容差、维护难等问题，FIRERPA 在大规模部署中的表现尤为出色。

<h3><p align="center">160+ APIs and full Python SDK for rapid development.</p></h3>
<p align="center">160+ API 配套完整 Python SDK，助力高效开发。</p>

FIRERPA offers over 160 categorized, stable APIs that cover command execution, system configuration, automation flows, and app-level controls. It also provides a full-featured Python SDK for rapid development and seamless AI integration. Developers can easily build intelligent workflows with precise control over Android systems. FIRERPA 提供超 160 个分类清晰、稳定可靠的接口，涵盖命令执行、系统设置、自动化流程与应用控制等，并包含完整的 Python SDK，帮助开发者高效实现与 AI 的无缝集成，构建具备精细控制能力的智能化任务流。

<p align="center">
<img src="image/inspect.png" alt="demo" width="95%">
</p>

<h3><p align="center">Remote desktop and device control, made simple.</p></h3>
<p align="center">远程桌面与设备控制，简单直观。</p>

FIRERPA comes with a clean, intuitive remote desktop interface, allowing users to monitor and control Android devices visually and interactively. Whether for testing, automation validation, or system diagnostics, it provides a powerful control layer with minimal setup. FIRERPA 内置简洁直观的远程桌面功能，帮助用户以可视化方式监控和操作 Android 设备。无论用于测试、任务验证还是系统诊断，都能提供高效、轻量的控制体验。

<p align="center">
<img src="image/demo.gif" alt="demo" width="95%">
</p>

Our project is not fully open source. Many industry tools also hide untraceable binaries in large codebases — a form of false advertising we reject. Due to the nature of this field, open-sourcing would raise defense costs and cause unnecessary trouble for both you and us. We choose to keep the tool free and accessible, focused on building the most effective solution. For security, we guarantee no malicious code, no backdoors, and no data collection. You’re free to reverse-engineer and verify its safety however you like. 我们的项目并未完全开源，您使用的很多业内工具其实也没有开源核心部分，他们在大量的代码内隐藏不可溯源的二进制文件，这显然是虚假宣传，我们不会这么向您宣传。我们这样做是由于行业的特殊性，开源意味着要投入更多的对抗成本，这显然会给您以及我们增加很多不必要的麻烦。我们会始终保持工具的免费及开放，我们只为打造最趁手的兵器。对于安全性，我们承诺没有任何恶意代码或后门植入，没有任何数据被收集上传，您可以以任意方式破解逆向分析其安全及隐私性。

<p align="center">Check out real usage examples and developer guides in the documentation.</p>
<p align="center">欢迎查阅 FIRERPA 使用文档，了解更多实际案例与开发指南。</p>