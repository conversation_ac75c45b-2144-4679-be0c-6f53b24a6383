#!/bin/bash
[ $# -lt 2 ] && exit 1
DEFAULT_ID_RSA=$(mktemp)
PORT=${PORT:-65000}
case "$1" in
                *':'*)
                                p1=root@$1
                                p2=$2
                ;;
                *)
                                p1=$1
                                p2=root@$2
                ;;
esac
umask 077
if [ ! -f "${CERTIFICATE}" ]; then
# this is the default id_rsa for ssh service
cat <<EOL >$DEFAULT_ID_RSA
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************EOL
else
DEFAULT_ID_RSA=$CERTIFICATE
fi
exec rsync -avz ${@:3} -e "ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no -o LogLevel=ERROR -i $DEFAULT_ID_RSA -p $PORT" $p1 $p2
