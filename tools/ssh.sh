#!/bin/bash
TARGET=${1:-localhost}
PORT=${PORT:-65000}
DEFAULT_ID_RSA=$(mktemp)

umask 077
if [ ! -f "${CERTIFICATE}" ]; then
# this is the default id_rsa for ssh service
cat <<EOL >$DEFAULT_ID_RSA
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************EOL
else
DEFAULT_ID_RSA=$CERTIFICATE
fi
ssh-add $DEFAULT_ID_RSA >/dev/null 2>&1 || true
exec ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no \
         -o LogLevel=ERROR -i $DEFAULT_ID_RSA -p $PORT root@$TARGET ${@:2}
