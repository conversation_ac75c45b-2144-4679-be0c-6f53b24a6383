// Copyright 2022 rev1si0n (<EMAIL>). All rights reserved.
//
// Distributed under MIT license.
// See file LICENSE for detail or copy at https://opensource.org/licenses/MIT
syntax = "proto3";
package lamda.rpc;

enum Group {
                GROUP_SYSTEM       = 0;
                GROUP_SECURE       = 1;
                GROUP_GLOBAL       = 2;
}

message SettingsRequest {
        Group  group    = 1;
        string name     = 2;
        string value    = 3;
}
