// Copyright 2022 rev1si0n (<EMAIL>). All rights reserved.
//
// Distributed under MIT license.
// See file LICENSE for detail or copy at https://opensource.org/licenses/MIT
syntax = "proto3";
package lamda.rpc;

import "application.proto";

enum GproxyType {
                SOCKS5          = 0;
                HTTP_CONNECT    = 1;
                HTTP_RELAY      = 2;
}

message GproxyConfigRequest {
        ApplicationInfo application = 1;
        GproxyType type             = 2;
        string nameserver           = 3;
        string login                = 4;
        string password             = 5;
        string host                 = 6;
        uint32 port                 = 7;
        bool bypass_local_subnet    = 8;
        bool drop_udp               = 9;
        bool udp_proxy              = 10;
        bool dns_proxy              = 11;
}

enum OpenVPNProto {
                TCP             = 0;
                UDP             = 1;
}

enum OpenVPNAuth {
                SHA1            = 0;
                SHA224          = 1;
                SHA256          = 2;
                SHA384          = 3;
                SHA512          = 4;
                RIPEMD160       = 5;
                RSA_SHA1        = 6;
                RSA_SHA224      = 7;
                RSA_SHA256      = 8;
                RSA_SHA384      = 9;
                RSA_SHA512      = 10;
                RSA_RIPEMD160   = 11;
}

enum OpenVPNCipher {
                AES_128_GCM      = 0;
                AES_256_GCM      = 1;
                CHACHA20_POLY1305= 2;
                AES_128_CBC      = 3;
                AES_256_CBC      = 4;
}

enum OpenVPNKeyDirection {
// because 0 is a default value
// so we use 1 as key-direction 0
                KEY_DIRECTION_NONE = 0;
                KEY_DIRECTION_0    = 1;
                KEY_DIRECTION_1    = 2;
}

enum OpenVPNEncryption {
                TLS_NONE        = 0;
                TLS_AUTH        = 1;
                TLS_CRYPT       = 2;
                TLS_CRYPT_V2    = 3;
}

message OpenVPNConfigRequest {
        bool all_traffic            = 1;
        OpenVPNProto    proto       = 2;
        string host                 = 3;
        uint32 port                 = 4;
        OpenVPNCipher   cipher      = 5;
        string ca                   = 6;
        string cert                 = 7;
        string key                  = 8;
        OpenVPNEncryption tls_encryption = 9;
        OpenVPNKeyDirection tls_key_direction = 10;
        string tls_key              = 11;
        OpenVPNAuth     auth        = 12;
        string          login       = 13;
        string          password    = 14;
}
