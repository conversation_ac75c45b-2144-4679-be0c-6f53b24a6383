# properties.local configuration file
#
# DO NOT DIRECTLY COPY THIS FILE, PLEASE SELECT THE NECESSARY CONFIGURATION LINES TO CREATE A NEW FILE
#
# This file is the LAMDA service configuration file. You can use
# it to automatically start some services when LAMDA starts.
# The file should be located in /data or /data/usr (this directory
# will not exist before the first startup, please create it if it does not exist).
# Lines starting with # and ; are comment lines and will be ignored.
# Please ensure that each line follows the format a=b and does not contain spaces.
# If there are duplicate configuration items across multiple files,
# the last duplicate will be used.

# Set the default listening port for LAMDA.
port=65000

# You can change the name displayed on the remote desktop for
# LAMDA to any name you prefer (up to 10 characters).
brandname=FIR<PERSON><PERSON>

# Set the LAMDA certificate. It will encrypt your remote desktop
# and API traffic, and enable password authentication. You can obtain
# the following configuration value by running 'base64 -w0 lamda.pem'.
# It is the base64-encoded certificate.
cert=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Set the remote desktop login password for LAMDA, 6-32 characters.
# This password will only take effect if a certificate (--certificate)
# is used. It serves as a backup password to the one generated in
# the certificate (for easier memorization).
ssl-web-credential=password123

# Set Access-Control-Allow-Origin header of firerpa webui
# and it's apis to allow you embed firerpa's features
# see https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Allow-Origin
allow_origin=https://example.com

# Setting this option will also disable the LAMDA Python client API.
# This option should be set only if the device crashes after starting LAMDA,
# or if you do not need to use the API. This option will also disable
# any components that might impact system stability.
disable-client-api=false

# Enhanced stealth mode, designed to protect LAMDA and its associated
# components from detection in cases where there is a risk of damaging
# itself or third-party components.
# Use only if necessary (introduced in version 7.65).
enhanced-stealth-mode=false

# Enhanced automation.
enhanced-automation=false

# Remote Desktop Touch Fix
# If there is an offset or no response when you touch the screen on
# the remote desktop, please set the following configuration:
touch.backend=system

# The switch for get_last_activities, default is false and does not intercept.
# introduced in 8.20
intercept-intent=false

# Set the log file location (directory must exists)
logfile=/data/local/tmp/server.log

# ---------- OpenVPN Configuration ----------
# Do not manually write the following configuration. You should use
# our accompanying OpenVPN server setup solution to set it up and
# generate this configuration using its built-in commands.
openvpn.proto=udp
openvpn.cipher=AES-256-GCM
openvpn.host=***************
openvpn.port=1190
openvpn.ca=LS0tLS1CRU...
openvpn.cert=LS0tLS1CRU...
openvpn.key=LS0tLS1CRU...
openvpn.tls_encryption=
openvpn.tls_key_direction=
openvpn.tls_key=
# ONLY THE FOLLOWING CONFIGURATION ITEMS CAN BE MANUALLY CONFIGURED.
# Whether to enable global VPN.
openvpn.global=false
# Whether to enable the service true | false
openvpn.enable=true

# ---------- Proxy Configuration ----------
# This configuration is used to make LAMDA automatically connect
# to the proxy server at startup.
#
# Whether to enable the service true | false
gproxy.enable=true
# The proxy type can be either http-connect or socks5.
gproxy.type=http-connect
# Proxy server address
gproxy.host=*********
# Proxy server port
gproxy.port=8080
# Proxy server login password (leave empty for no authentication)
gproxy.password=
# Proxy server login username (leave empty for no authentication)
gproxy.login=

# ---------- CRONTAB Scheduled Task Service ----------
# If you do not need scheduled tasks, you can disable this service.
# Whether to enable the service true | false
cron.enable=true

# ---------- SSHD Service ----------
# If you do not need to connect to the device via SSH, you can disable this service.
# Whether to enable the service true | false
sshd.enable=true

# ---------- Port Forwarding (frp) Service ----------
# Whether to enable the service true | false
fwd.enable=true
# Port to forward to the remote (0 means randomly allocated)
fwd.rport=0
# FRP server address
fwd.host=***************
# FRP server port
fwd.port=9911
# FRP protocol
fwd.protocol=tcp
# FRP login authentication
fwd.token=abc123

# ---------- ADB Service ----------
# Whether to enable the service true | false
adb.enable=true
# Default working directory for built-in ADB (adb shell working directory)
adb.directory=/data/local/tmp
# If set to true, the ADB connection will have root privileges,
# otherwise it will have shell privileges. When this option is set to false,
# you will be using a native-like adb shell, and you will not be able to use
# LAMDA's built-in commands. Please note that since ADB does not use TLS connections,
# traffic may be monitored. For security reasons, when the LAMDA service is
# started with a certificate, this value will be set to false by default.
# However, if you specify it in the properties.local file, the configuration
# in the file will take precedence.
# You are responsible for ensuring security.
adb.privileged=true

# ---------- Bridge Proxy Service ----------
# Whether to enable the service true | false
tunnel2.enable=true
# The bridge proxy will require login authentication only if both login and password
# are set. If either one is left empty, no login authentication will be required.
tunnel2.login=lamda
# Login password for the bridge proxy
tunnel2.password=1234
# Outbound interface (rmnet|wlan)
# When the outbound interface is rmnet, the proxy will attempt to forward your requests through mobile data.
# When the outbound interface is wlan, requests will be forwarded through the wlan interface.
# If the configuration is empty, the default network will be used to forward the requests.
tunnel2.iface=rmnet

# ---------- mDNS Broadcast Service ----------
# Enable or disable true | false
mdns.enable=true
# Add TXT metadata for mDNS. When enabled, it will support querying device information
# such as model, ABI, and device ID using tools like python-zeroconf. Disabled by default.
mdns.meta=false
# Set the broadcast domain name using a locally unique ID, default is {DEVICEID-UNIQUE}.lamda.
# If the name duplicates in the local network, a suffix ID will be automatically added.
mdns.name=DEVICEID-UNIQUE.lamda
# Set the broadcast service name, default is lamda, i.e., _lamda._tcp.local.
mdns.service=lamda
