# Copyright 2022 rev1si0n (https://github.com/rev1si0n). All rights reserved.
#
# Distributed under MIT license.
# See file LICENSE for detail or copy at https://opensource.org/licenses/MIT
class CompatibilityException(Exception):
    """ Exception """
class DeadSystemException(Exception):
    """ Exception """
class DeviceUnavailable(Exception):
    """ Exception """
class DuplicateEntryError(Exception):
    """ Exception """
class IllegalArgumentException(Exception):
    """ Exception """
class IllegalStateException(Exception):
    """ Exception """
class InstallPackageFailed(Exception):
    """ Exception """
class InternalRpcException(Exception):
    """ Exception """
class InvalidAndroidPackage(Exception):
    """ Exception """
class InvalidArgumentError(Exception):
    """ Exception """
class InvalidOperationError(Exception):
    """ Exception """
class InvalidRootCertificate(Exception):
    """ Exception """
class MethodNotFoundException(Exception):
    """ Exception """
class NameNotFoundException(Exception):
    """ Exception """
class NotImplementedException(Exception):
    """ Exception """
class NullPointerException(Exception):
    """ Exception """
class SecurityException(Exception):
    """ Exception """
class ServiceUnavailable(Exception):
    """ Exception """
class StaleObjectException(Exception):
    """ Exception """
class StartupActivityNotFound(Exception):
    """ Exception """
class StorageOutOfMemory(Exception):
    """ Exception """
class UiAutomatorException(Exception):
    """ Exception """
class UiObjectNotFoundException(Exception):
    """ Exception """
class UnHandledException(Exception):
    """ Exception """